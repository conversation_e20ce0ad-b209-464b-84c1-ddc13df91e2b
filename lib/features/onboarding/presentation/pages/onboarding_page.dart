import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/router/app_routes.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../providers/onboarding_provider.dart';
import '../widgets/onboarding_step_indicator.dart';

import '../widgets/personal_info_step.dart';
import '../widgets/physical_info_step.dart';
import '../widgets/health_info_step.dart';
import '../widgets/goals_step.dart';
import '../widgets/preferences_step.dart';
import '../widgets/notifications_step.dart';
import '../widgets/complete_step.dart';

class OnboardingPage extends ConsumerStatefulWidget {
  const OnboardingPage({super.key});

  @override
  ConsumerState<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends ConsumerState<OnboardingPage> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    final currentStep = ref.read(onboardingNotifierProvider).currentStep;
    _pageController = PageController(
      initialPage: OnboardingStep.values.indexOf(currentStep),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    final l10n = AppLocalizations.of(context);

    // Listen for step changes and completion
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.currentStep != next.currentStep) {
        // Sync PageView with state changes
        final newIndex = OnboardingStep.values.indexOf(next.currentStep);
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    // Listen for onboarding completion (when app state changes)
    ref.listen(appStateNotifierProvider, (previous, next) {
      // Check if onboarding was just completed
      if (previous?.isOnboardingCompleted == false && next.isOnboardingCompleted == true) {
        // Navigate to home
        if (context.mounted) {
          context.go(AppRoutes.home);
        }
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: kDebugMode ? AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            tooltip: 'Pre-fill with test data',
            onPressed: () {
              onboardingNotifier.preFillWithTestData();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Onboarding data pre-filled with test values'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
          ),
        ],
      ) : null,
      body: SafeArea(
        child: Column(
          children: [

            // Progress indicator
            if (onboardingState.currentStep != OnboardingStep.complete)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: OnboardingStepIndicator(
                  currentStep: onboardingState.currentStep,
                  progress: onboardingNotifier.progress,
                ),
              ),

            // Step content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  onboardingNotifier.goToStep(OnboardingStep.values[index]);
                },
                children: [
                  PersonalInfoStep(),
                  PhysicalInfoStep(),
                  HealthInfoStep(),
                  GoalsStep(),
                  PreferencesStep(),
                  NotificationsStep(),
                  CompleteStep(),
                ],
              ),
            ),

            // Navigation buttons
            // if (onboardingState.currentStep != OnboardingStep.complete)
            Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Back button
                    if (onboardingState.currentStep != OnboardingStep.personalInfo)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: onboardingNotifier.previousStep,
                          child: Text(l10n.back),
                        ),
                      ),

                    if (onboardingState.currentStep != OnboardingStep.personalInfo)
                      const SizedBox(width: 16),

                    // Next/Complete button
                    Expanded(
                      flex: onboardingState.currentStep == OnboardingStep.personalInfo ? 1 : 1,
                      child: ElevatedButton(
                        onPressed: (onboardingNotifier.canProceed || onboardingState.currentStep == OnboardingStep.notifications)
                            ? () {
                                if (onboardingState.currentStep == OnboardingStep.notifications) {
                                  onboardingNotifier.goToCompleteStep();
                                } else {
                                  onboardingNotifier.nextStep();
                                }
                              }
                            : null,
                        child: onboardingState.isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : Text(
                                onboardingState.currentStep == OnboardingStep.notifications
                                    ? l10n.complete
                                    : l10n.next,
                              ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
