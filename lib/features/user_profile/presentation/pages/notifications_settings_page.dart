import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../../onboarding/presentation/widgets/notifications_step.dart';
import '../../../onboarding/providers/onboarding_provider.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../core/services/firestore_service.dart';

class NotificationsSettingsPage extends ConsumerWidget {
  const NotificationsSettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'إعدادات الإشعارات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          TextButton(
            onPressed: () => _saveNotificationSettings(context, ref),
            child: const Text(
              'حفظ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Notifications content - hide the profile message since we're already in profile
            const Expanded(
              child: NotificationsStep(showProfileMessage: false),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveNotificationSettings(BuildContext context, WidgetRef ref) async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري حفظ الإعدادات...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Get current user and onboarding data
      final currentUser = ref.read(currentUserProvider);
      final onboardingData = ref.read(onboardingNotifierProvider).data;

      if (currentUser != null) {
        // Save to Firestore
        final firestoreService = ref.read(firestoreServiceProvider);
        await firestoreService.updateUserProfileFields({
          'mealReminders': onboardingData.mealReminders,
          'waterReminders': onboardingData.waterReminders,
          'workoutReminders': onboardingData.workoutReminders,
          'progressUpdates': onboardingData.progressUpdates,
        });
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ إعدادات الإشعارات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Navigate back
        context.pop();
      }
    } catch (e) {
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
