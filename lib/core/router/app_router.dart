import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/welcome_auth_page.dart';
import '../../features/intro_wizard/presentation/pages/intro_wizard_page.dart';
import '../../features/meal_planning/presentation/pages/home_page.dart';
import '../../features/meal_planning/presentation/pages/meal_detail_page.dart';
import '../../features/meal_planning/presentation/pages/meal_planning_page.dart';
import '../../features/user_profile/presentation/pages/profile_page.dart';
import '../../features/user_profile/presentation/pages/settings_page.dart';
import '../../features/subscription/presentation/pages/subscription_page.dart';
import '../../features/onboarding/presentation/pages/onboarding_page.dart';
import '../../features/onboarding/presentation/pages/settings_onboarding_page.dart';
import '../../features/onboarding/providers/onboarding_provider.dart';
// import '../../shared/providers/auth_provider.dart';
import '../../shared/providers/app_state_provider.dart';
import 'app_routes.dart';

part 'app_router.g.dart';

@riverpod
GoRouter appRouter(AppRouterRef ref) {
  // final authState = ref.watch(authNotifierProvider);
  final appState = ref.watch(appStateNotifierProvider);

  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final currentPath = state.uri.path;

      // If not initialized, stay on splash
      if (!appState.isInitialized) {
        return AppRoutes.splash;
      }

      // Check what the user needs in order of priority
      final needsIntroWizard = appState.isFirstLaunch && !appState.hasSeenIntroWizard;
      final needsWelcome = appState.hasSeenIntroWizard && !appState.hasSeenWelcome;
      final needsOnboarding = appState.hasSeenWelcome && !appState.isOnboardingCompleted;

      // Priority 1: Intro wizard (only if first launch and hasn't seen it)
      if (needsIntroWizard && currentPath != AppRoutes.introWizard) {
        return AppRoutes.introWizard;
      }

      // Priority 2: Welcome auth (only if seen intro but not welcome)
      if (needsWelcome && currentPath != AppRoutes.welcomeAuth) {
        return AppRoutes.welcomeAuth;
      }

      // Priority 3: Onboarding (only if seen welcome but not completed onboarding)
      if (needsOnboarding && !currentPath.startsWith('/onboarding')) {
        return AppRoutes.onboarding;
      }

      // If everything is completed and user is on initial screens, redirect to home
      if (!needsIntroWizard && !needsWelcome && !needsOnboarding &&
          (currentPath == AppRoutes.splash ||
           currentPath == AppRoutes.introWizard ||
           currentPath == AppRoutes.welcomeAuth ||
           currentPath.startsWith('/onboarding'))) {
        return AppRoutes.home;
      }

      return null; // No redirect needed
    },
    routes: [
      // Splash Route
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Introduction Wizard Route
      GoRoute(
        path: AppRoutes.introWizard,
        name: 'intro-wizard',
        builder: (context, state) => const IntroWizardPage(),
      ),

      // Welcome Auth Route
      GoRoute(
        path: AppRoutes.welcomeAuth,
        name: 'welcome-auth',
        builder: (context, state) => const WelcomeAuthPage(),
      ),

      // Auth Routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Main App Routes
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const HomePage(),
        routes: [
          GoRoute(
            path: 'meal/:mealId',
            name: 'meal-detail',
            builder: (context, state) {
              final mealId = state.pathParameters['mealId']!;
              return MealDetailPage(mealId: mealId);
            },
          ),
        ],
      ),

      // Profile Route
      GoRoute(
        path: AppRoutes.profile,
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
            routes: [
              GoRoute(
                path: 'personal',
                name: 'personal-settings',
                builder: (context, state) {
                  // Get the step parameter from query parameters
                  final stepParam = state.uri.queryParameters['step'];
                  OnboardingStep? initialStep;

                  if (stepParam != null) {
                    // Convert string to OnboardingStep enum
                    try {
                      initialStep = OnboardingStep.values.firstWhere(
                        (step) => step.name == stepParam,
                      );
                    } catch (e) {
                      // If invalid step, use null (will default to current step)
                      initialStep = null;
                    }
                  }

                  return SettingsOnboardingPage(initialStep: initialStep);
                },
              ),
            ],
          ),
        ],
      ),

      // Meal Planning Routes
      GoRoute(
        path: AppRoutes.mealPlanning,
        name: 'meal-planning',
        builder: (context, state) => const MealPlanningPage(),
      ),

      // Subscription Route
      GoRoute(
        path: AppRoutes.subscription,
        name: 'subscription',
        builder: (context, state) => const SubscriptionPage(),
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في التنقل',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
}
